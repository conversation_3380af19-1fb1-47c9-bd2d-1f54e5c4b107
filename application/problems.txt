
> wizlop@0.1.0 build:webpack
> webpack --config config/webpack/webpack.dev.js

asset bundle.js 4.58 MiB [compared for emit] [big] (name: main)
runtime modules 1.04 KiB 5 modules
modules by path ./node_modules/ 1.48 MiB 109 modules
modules by path ./app/ 146 KiB
  modules by path ./app/landing/components/ 110 KiB 14 modules
  modules by path ./app/landing/utils/*.ts 13.3 KiB
    ./app/landing/utils/scrollAnimations.ts 4.46 KiB [built] [code generated]
    ./app/landing/utils/responsiveUtils.ts 8.86 KiB [built] [code generated]
  ./app/page.tsx 1.13 KiB [built] [code generated]
  ./app/colors.ts 5.42 KiB [built] [code generated]
  ./app/shared/poi/constants.ts 15.9 KiB [built] [code generated]
webpack 5.100.1 compiled successfully in 3696 ms
