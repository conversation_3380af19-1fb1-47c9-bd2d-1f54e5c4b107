/** @format */

// Wizlop Color System - Centralized color palette
// Based on Logo 5 gradient colors for consistent branding

const modernColorsBase = {
	// 1. Primary Brand Colors (3 core brand colors) - Enhanced for modernity
	brand: {
		primary: '#0EA5E9', // Modern Sky Blue - Main brand anchor (buttons, nav, footer)
		secondary: '#1E293B', // Modern Slate - Secondary brand tone (hover, text, headers)
		accent: '#22C55E', // Vibrant Green - Nature-focused accent (tabs, highlights)
	},

	// 2. Supporting Colors (6 supporting colors) - Vibrant and eye-catching
	supporting: {
		'sup-color1': '#FF6B6B', // Vibrant Coral Red - Eye-catching accent
		'sup-color2': '#4ECDC4', // Bright Turquoise - Fresh and modern
		'sup-color3': '#45B7D1', // Electric Blue - Dynamic and energetic
		'sup-color4': '#96CEB4', // Mint Green - Soft but vibrant
		'sup-color5': '#FFEAA7', // Warm Yellow - Attention-grabbing
		'sup-color6': '#DDA0DD', // Soft Purple - Elegant and modern
	},

	// 3. Neutral Colors - Refined for modern look
	neutral: {
		'text-primary': '#0F172A', // Deeper primary text color
		'text-secondary': '#475569', // Modern secondary text, inputs
		'bg-primary': '#FFFFFF', // Pure white background
		'bg-secondary': '#F8FAFC', // Light background sections
	},

	// 4. Utility Colors - More vibrant and clear
	utility: {
		'state-success': '#16A34A', // Vibrant Success green
		'state-warning': '#EA580C', // Bright Warning orange
		'state-error': '#DC2626', // Clear Error red
		'state-error-light': '#FEE2E2', // Light error background
		'state-info': '#2563EB', // Modern Information blue
	},

	// 5. UI Colors (modern and vibrant variations)
	ui: {
		// Light variations for backgrounds - Modern and bright
		'shade-1': '#F0F9FF', // Very light modern blue
		'shade-2': '#E0F2FE', // Light modern blue
		'shade-3': '#BAE6FD', // Medium light modern blue

		'neutral-1': '#F8FAFC', // Very light modern slate
		'neutral-2': '#F1F5F9', // Light modern slate

		'accent-1': '#F0FDF4', // Very light modern green
		'accent-2': '#DCFCE7', // Light modern green
		'accent-3': '#BBF7D0', // Medium light modern green

		// Essential gray scale - Modern and refined
		'gray-1': '#F8FAFC',
		'gray-2': '#F1F5F9',
		'gray-3': '#E2E8F0',
		'gray-4': '#CBD5E1',
		'gray-5': '#94A3B8',
		'gray-6': '#64748B',
	},
} as const;

// Backward compatibility - Add old color structure for existing code
export const colors = {
	...modernColorsBase,
	brand: {
		...modernColorsBase.brand,
		blue: modernColorsBase.brand.primary,
		navy: modernColorsBase.brand.secondary,
		green: modernColorsBase.brand.accent,
	},
	neutral: {
		...modernColorsBase.neutral,
		textBlack: modernColorsBase.neutral['text-primary'],
		slateGray: modernColorsBase.neutral['text-secondary'],
		cloudWhite: modernColorsBase.neutral['bg-primary'],
		lightMistGray: modernColorsBase.neutral['bg-secondary'],
	},
	supporting: {
		...modernColorsBase.supporting,
		lightBlue: modernColorsBase.supporting['sup-color2'],
		softNavy: modernColorsBase.brand.secondary,
		mintGreen: modernColorsBase.supporting['sup-color4'],
		teal: modernColorsBase.supporting['sup-color2'],
		darkBlue: modernColorsBase.supporting['sup-color3'],
		purple: modernColorsBase.supporting['sup-color6'],
	},
};

// Original modern colors structure available as 'modernColors'
export const modernColors = modernColorsBase;

// Tailwind CSS custom color classes
export const tailwindColors = {
	// Primary brand colors
	'wizlop-blue': colors.brand.blue,
	'wizlop-navy': colors.brand.navy,
	'wizlop-green': colors.brand.green,

	// Supporting colors
	'wizlop-light-blue': colors.supporting.lightBlue,
	'wizlop-soft-navy': colors.supporting.softNavy,
	'wizlop-mint-green': colors.supporting.mintGreen,
	'wizlop-teal': colors.supporting.teal,
	'wizlop-dark-blue': colors.supporting.darkBlue,
	'wizlop-purple': colors.supporting.purple,

	// Neutrals
	'wizlop-text': colors.neutral.textBlack,
	'wizlop-slate': colors.neutral.slateGray,
	'wizlop-cloud': colors.neutral.cloudWhite,
	'wizlop-mist': colors.neutral.lightMistGray,
} as const;

// Gradient combinations
export const gradients = {
	primary: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.navy} 100%)`,
	secondary: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
	soft: `linear-gradient(135deg, ${colors.supporting.lightBlue} 0%, ${colors.supporting.mintGreen} 100%)`,
	background: `linear-gradient(135deg, ${colors.ui.blue50} 0%, ${colors.ui.green50} 100%)`,
} as const;

// Common color utilities
export const getColorWithOpacity = (color: string, opacity: number): string => {
	return `${color}${Math.round(opacity * 255)
		.toString(16)
		.padStart(2, '0')}`;
};

export default colors;
