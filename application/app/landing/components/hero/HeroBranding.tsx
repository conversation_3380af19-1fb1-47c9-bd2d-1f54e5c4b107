/** @format */

'use client';

import { colors } from '@/app/colors';
import {
	LANDING_PAGE_DATA,
	getPOICategories,
	getPOISubcategories,
} from '@/app/shared/poi/constants';
import React, { useEffect, useState } from 'react';

// Hook for responsive screen size detection
const useScreenSize = () => {
	const [screenSize, setScreenSize] = useState<'mobile' | 'tablet' | 'desktop'>(
		'desktop'
	);

	useEffect(() => {
		const checkScreenSize = () => {
			const width = window.innerWidth;
			if (width < 768) {
				setScreenSize('mobile');
			} else if (width < 1024) {
				setScreenSize('tablet');
			} else {
				setScreenSize('desktop');
			}
		};

		checkScreenSize();
		window.addEventListener('resize', checkScreenSize);
		return () => window.removeEventListener('resize', checkScreenSize);
	}, []);

	return screenSize;
};

interface HeroBrandingProps {
	onGetStarted: () => void;
}

const HeroBranding: React.FC<HeroBrandingProps> = ({ onGetStarted }) => {
	// Get real data counts
	const categoriesCount = getPOICategories().length;
	const subcategoriesCount = getPOISubcategories().length;
	const [showInfoCards, setShowInfoCards] = useState(false);
	const screenSize = useScreenSize();
	const [letterAnimations, setLetterAnimations] = useState([
		false,
		false,
		false,
		false,
		false,
		false,
	]); // Controls individual letter animations for "Wizlop"

	// Letter-by-letter animation sequence
	useEffect(() => {
		const timer1 = setTimeout(() => {
			setShowInfoCards(true); // Show info cards at the same time as letter animations

			// Animate letters one by one: W-i-z-l-o-p
			const letters = ['W', 'i', 'z', 'l', 'o', 'p'];
			letters.forEach((_, index) => {
				setTimeout(() => {
					setLetterAnimations((prev) => {
						const newAnimations = [...prev];
						newAnimations[index] = true;
						return newAnimations;
					});
				}, index * 150); // 150ms delay between each letter
			});
		}, 500); // Start letter animations after 0.5 seconds

		return () => {
			clearTimeout(timer1);
		};
	}, []);

	return (
		<div className='relative w-full'>
			{/* Compact Hero Layout - Following rules.txt */}
			<div className='relative overflow-hidden'>
				{/* Subtle Background Elements */}
				<div className='absolute inset-0 opacity-5'>
					{/* Animated geometric shapes with enhanced effects */}
					<div
						className='absolute top-1/4 left-0 w-40 h-40 rounded-full animate-pulse'
						style={{
							background: `radial-gradient(circle, ${colors.brand.blue}60 0%, ${colors.brand.blue}20 40%, transparent 70%)`,
							animation: 'pulse 4s ease-in-out infinite',
							animationDelay: '0s',
						}}
					/>
					<div
						className='absolute top-1/2 right-0 w-32 h-32 rounded-lg animate-pulse'
						style={{
							background: `linear-gradient(45deg, ${colors.brand.green}50 0%, ${colors.brand.green}20 50%, transparent 70%)`,
							transform: 'rotate(45deg)',
							animation: 'pulse 3s ease-in-out infinite',
							animationDelay: '1s',
						}}
					/>
					<div
						className='absolute bottom-1/4 left-1/3 w-28 h-28 rounded-full animate-pulse'
						style={{
							background: `conic-gradient(from 0deg, ${colors.brand.navy}60, ${colors.brand.blue}40, transparent, ${colors.brand.green}30)`,
							animation: 'pulse 5s ease-in-out infinite',
							animationDelay: '2s',
						}}
					/>

					{/* Additional floating elements for more depth */}
					<div
						className='absolute top-3/4 right-1/4 w-16 h-16 rounded-full animate-pulse'
						style={{
							background: `radial-gradient(circle, ${colors.supporting['sup-color2']}40 0%, transparent 60%)`,
							animation: 'pulse 3.5s ease-in-out infinite',
							animationDelay: '1.5s',
						}}
					/>
					<div
						className='absolute top-1/6 right-1/3 w-12 h-12 rounded-lg animate-pulse'
						style={{
							background: `linear-gradient(135deg, ${colors.brand.blue}30 0%, transparent 70%)`,
							transform: 'rotate(30deg)',
							animation: 'pulse 4.5s ease-in-out infinite',
							animationDelay: '0.5s',
						}}
					/>

					{/* Subtle grid pattern overlay */}
					<div
						className='absolute inset-0 opacity-5'
						style={{
							backgroundImage: `linear-gradient(${colors.brand.blue}20 1px, transparent 1px), linear-gradient(90deg, ${colors.brand.blue}20 1px, transparent 1px)`,
							backgroundSize: '50px 50px',
						}}
					/>
				</div>

				{/* Main Content - Compact Two Column Layout */}
				<div className='relative z-10 py-8 md:py-12 lg:py-16'>
					<div className='w-full px-4 md:px-6 lg:px-12 max-w-7xl mx-auto'>
						<div className='grid grid-cols-1 lg:grid-cols-12 gap-4 md:gap-6 lg:gap-8'>
							{/* Left Column - Brand Identity */}
							<div className='lg:col-span-7 flex items-center min-h-[50vh]'>
								<div className='text-left space-y-3 md:space-y-4 w-full'>
									{/* Logo and Brand Name - Left Aligned Layout */}
									<div className='flex flex-row items-center space-x-4'>
										{/* Logo - Much Smaller */}
										<div className='flex-shrink-0'>
											<img
												src='/logo/512x512.png'
												alt='Wizlop Logo'
												className='object-contain transition-all duration-1000'
												style={{
													width:
														screenSize === 'mobile'
															? '3rem'
															: screenSize === 'tablet'
															? '4rem'
															: '5rem',
													height:
														screenSize === 'mobile'
															? '3rem'
															: screenSize === 'tablet'
															? '4rem'
															: '5rem',
												}}
											/>
										</div>

										{/* Brand Name - Much Smaller */}
										<div>
											<h1
												className='font-black tracking-tight leading-none relative overflow-hidden'
												style={{
													fontSize:
														screenSize === 'mobile'
															? '1.5rem'
															: screenSize === 'tablet'
															? '2rem'
															: '2.5rem',
												}}>
												{/* Wizlop - Letter by letter animated entrance */}
												<span
													className='inline-block relative'
													style={{ zIndex: 5 }}>
													{['W', 'i', 'z', 'l', 'o', 'p'].map(
														(letter, index) => (
															<span
																key={index}
																className='inline-block relative'
																style={{
																	backgroundImage: `linear-gradient(135deg, ${colors.brand.secondary} 0%, ${colors.brand.primary} 50%, ${colors.brand.accent} 100%)`,
																	WebkitBackgroundClip: 'text',
																	WebkitTextFillColor: 'transparent',
																	backgroundClip: 'text',
																	transform: letterAnimations[index]
																		? 'translateX(0) scale(1)'
																		: 'translateX(-30px) scale(0.3)',
																	opacity: letterAnimations[index] ? 1 : 0,
																	transition:
																		'all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
																}}>
																{letter}
															</span>
														)
													)}
												</span>
											</h1>
										</div>
									</div>

									{/* Tagline - Smaller */}
									<div className='relative'>
										<p
											className='font-semibold tracking-wide'
											style={{
												color: colors.brand.primary,
												fontSize:
													screenSize === 'mobile'
														? '0.875rem'
														: screenSize === 'tablet'
														? '1rem'
														: '1.125rem',
											}}>
											Explore Life, Experience Everything
										</p>
									</div>
								</div>
							</div>

							{/* Right Column - Info Cards & CTA */}
							<div className='lg:col-span-5 space-y-4 md:space-y-6'>
								{/* Main Description Card */}
								<div
									className='relative transition-all duration-1000'
									style={{
										opacity: showInfoCards ? 1 : 0,
										transform: showInfoCards
											? 'translateY(0) scale(1)'
											: 'translateY(20px) scale(0.95)',
										transitionDelay: '0ms',
									}}>
									<div
										className='p-4 md:p-6 border backdrop-blur-sm relative overflow-hidden'
										style={{
											background: `linear-gradient(135deg, ${colors.neutral.cloudWhite}95 0%, ${colors.ui.blue50}30 100%)`,
											borderColor: colors.ui.gray200,
											boxShadow: '0 12px 40px rgba(51, 194, 255, 0.12)',
										}}>
										{/* Animated background pattern */}
										<div
											className='absolute inset-0 opacity-5'
											style={{
												backgroundImage: `radial-gradient(circle at 20% 50%, ${colors.brand.blue} 0%, transparent 50%), radial-gradient(circle at 80% 50%, ${colors.brand.green} 0%, transparent 50%)`,
											}}
										/>

										<div className='relative z-10'>
											<div className='flex items-start space-x-3 md:space-x-4 mb-4'>
												<div
													className='w-10 h-10 md:w-12 md:h-12 flex items-center justify-center flex-shrink-0'
													style={{
														background: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
													}}>
													<span className='text-white text-lg md:text-xl'>
														🚀
													</span>
												</div>
												<div className='flex-1 min-w-0'>
													<h3
														className='text-base md:text-lg lg:text-xl font-bold mb-2'
														style={{ color: colors.brand.navy }}>
														Advanced Location Technology
													</h3>
													<p
														className='text-sm md:text-base leading-relaxed font-medium'
														style={{ color: colors.neutral.slateGray }}>
														{LANDING_PAGE_DATA.branding.heroDescription}
													</p>
												</div>
											</div>
										</div>
									</div>

									{/* Floating accent elements */}
									<div
										className='absolute -top-2 -right-2 w-6 h-6 rounded-full animate-pulse'
										style={{
											background: `linear-gradient(45deg, ${colors.brand.green} 0%, ${colors.brand.blue} 100%)`,
										}}
									/>
									<div
										className='absolute -bottom-2 -left-2 w-4 h-4 rounded-full animate-pulse'
										style={{
											background: `linear-gradient(45deg, ${colors.brand.blue} 0%, ${colors.supporting.lightBlue} 100%)`,
											animationDelay: '1s',
										}}
									/>
								</div>

								{/* Feature Highlights Grid */}
								<div
									className='grid grid-cols-1 md:grid-cols-2 gap-3 transition-all duration-200'
									style={{
										opacity: showInfoCards ? 1 : 0,
										transform: showInfoCards
											? 'translateY(0) scale(1)'
											: 'translateY(30px) scale(0.95)',
										transitionDelay: '0ms',
									}}>
									{/* Spatial Search Card */}
									<div
										className='p-2 md:p-3 border backdrop-blur-sm relative overflow-hidden'
										style={{
											background: `linear-gradient(135deg, ${colors.ui.blue50}80 0%, ${colors.neutral.cloudWhite}90 100%)`,
											borderColor: colors.ui.gray200,
											boxShadow: '0 6px 20px rgba(51, 194, 255, 0.08)',
										}}>
										<div className='text-center'>
											<div
												className='w-6 h-6 md:w-8 md:h-8 mx-auto mb-2 flex items-center justify-center'
												style={{
													background: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.supporting.lightBlue} 100%)`,
												}}>
												<span className='text-white text-xs md:text-sm'>
													⚡
												</span>
											</div>
											<h4
												className='text-xs md:text-sm font-bold mb-1'
												style={{ color: colors.brand.navy }}>
												Spatial Engine
											</h4>
											<p
												className='text-xs'
												style={{ color: colors.neutral.slateGray }}>
												Sub-second search results
											</p>
										</div>
									</div>

									{/* AI + Algorithms Card */}
									<div
										className='p-2 md:p-3 border backdrop-blur-sm relative overflow-hidden'
										style={{
											background: `linear-gradient(135deg, ${colors.ui.green50}80 0%, ${colors.neutral.cloudWhite}90 100%)`,
											borderColor: colors.ui.gray200,
											boxShadow: '0 6px 20px rgba(34, 197, 94, 0.08)',
										}}>
										<div className='text-center'>
											<div
												className='w-6 h-6 md:w-8 md:h-8 mx-auto mb-2 flex items-center justify-center'
												style={{
													background: `linear-gradient(135deg, ${colors.brand.green} 0%, ${colors.brand.blue} 100%)`,
												}}>
												<span className='text-white text-xs md:text-sm'>
													🧠
												</span>
											</div>
											<h4
												className='text-xs md:text-sm font-bold mb-1'
												style={{ color: colors.brand.navy }}>
												AI + Algorithms
											</h4>
											<p
												className='text-xs'
												style={{ color: colors.neutral.slateGray }}>
												Smart + Fast discovery
											</p>
										</div>
									</div>
								</div>

								{/* Enhanced CTA Section with Animation */}
								<div
									className='space-y-4 md:space-y-6 transition-all duration-1000'
									style={{
										opacity: showInfoCards ? 1 : 0,
										transform: showInfoCards
											? 'translateY(0) scale(1)'
											: 'translateY(40px) scale(0.95)',
										transitionDelay: '0ms',
									}}>
									{/* Futuristic CTA Button */}
									<div className='relative'>
										<button
											onClick={onGetStarted}
											className='group relative w-full lg:w-auto px-6 py-3 md:px-8 md:py-4 font-bold text-base md:text-lg transition-all duration-200 transform hover:scale-105 active:scale-95 overflow-hidden'
											style={{
												background: `linear-gradient(135deg, ${colors.brand.blue} 0%, ${colors.brand.green} 100%)`,
												color: 'white',
												boxShadow: '0 15px 35px rgba(51, 194, 255, 0.25)',
											}}>
											{/* Animated background layers */}
											<div
												className='absolute inset-0 opacity-0 group-hover:opacity-100 transition-all duration-200'
												style={{
													background: `linear-gradient(135deg, ${colors.brand.green} 0%, ${colors.brand.blue} 100%)`,
												}}
											/>

											{/* Shimmer effect */}
											<div
												className='absolute inset-0 opacity-0 group-hover:opacity-30 transition-all duration-200'
												style={{
													background: `linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.5) 50%, transparent 70%)`,
													transform: 'translateX(-100%)',
													animation: 'shimmer 2s infinite',
												}}
											/>

											<span className='relative z-10 flex items-center justify-center space-x-2'>
												<span>Start Exploring</span>
												<span className='text-xl group-hover:animate-bounce'>
													🚀
												</span>
											</span>

											{/* Enhanced ripple effect */}
											<div
												className='absolute inset-0 rounded-2xl opacity-0 group-active:opacity-100 transition-opacity duration-200'
												style={{
													background: `radial-gradient(circle, rgba(255,255,255,0.4) 0%, transparent 70%)`,
												}}
											/>
										</button>

										{/* Floating particles around button */}
										<div className='absolute -inset-4 pointer-events-none'>
											{Array.from({ length: 6 }).map((_, i) => (
												<div
													key={i}
													className='absolute w-2 h-2 rounded-full animate-pulse'
													style={{
														left: `${20 + i * 15}%`,
														top: `${10 + Math.sin(i) * 80}%`,
														background:
															i % 2 === 0
																? colors.brand.blue
																: colors.brand.green,
														opacity: 0.6,
														animationDelay: `${i * 0.3}s`,
														animationDuration: `${2 + Math.random()}s`,
													}}
												/>
											))}
										</div>
									</div>

									{/* Enhanced Animated Stats */}
									<div className='relative'>
										<div className='flex items-center justify-center space-x-4 md:space-x-6 lg:space-x-8 text-sm'>
											{/* Categories Stat */}
											<div className='text-center group'>
												<div
													className='font-bold text-lg md:text-xl lg:text-2xl mb-1 transition-all duration-500 group-hover:scale-110'
													style={{
														color: colors.brand.blue,
														textShadow: `0 0 20px ${colors.brand.blue}40`,
													}}>
													{categoriesCount}+
												</div>
												<div
													className='text-xs font-medium tracking-wide uppercase'
													style={{ color: colors.neutral.slateGray }}>
													Categories
												</div>
												{/* Animated underline */}
												<div
													className='h-0.5 w-0 group-hover:w-full transition-all duration-500 mx-auto mt-1'
													style={{ background: colors.brand.blue }}
												/>
											</div>

											{/* Animated divider */}
											<div
												className='w-px h-12 relative overflow-hidden'
												style={{ background: colors.ui.gray200 }}>
												<div
													className='absolute inset-0 w-full animate-pulse'
													style={{
														background: `linear-gradient(180deg, transparent 0%, ${colors.brand.blue}40 50%, transparent 100%)`,
													}}
												/>
											</div>

											{/* Locations Stat */}
											<div className='text-center group'>
												<div
													className='font-bold text-lg md:text-xl lg:text-2xl mb-1 transition-all duration-500 group-hover:scale-110'
													style={{
														color: colors.brand.green,
														textShadow: `0 0 20px ${colors.brand.green}40`,
													}}>
													{subcategoriesCount}+
												</div>
												<div
													className='text-xs font-medium tracking-wide uppercase'
													style={{ color: colors.neutral.slateGray }}>
													Locations
												</div>
												{/* Animated underline */}
												<div
													className='h-0.5 w-0 group-hover:w-full transition-all duration-500 mx-auto mt-1'
													style={{ background: colors.brand.green }}
												/>
											</div>

											{/* Animated divider */}
											<div
												className='w-px h-12 relative overflow-hidden'
												style={{ background: colors.ui.gray200 }}>
												<div
													className='absolute inset-0 w-full animate-pulse'
													style={{
														background: `linear-gradient(180deg, transparent 0%, ${colors.brand.green}40 50%, transparent 100%)`,
														animationDelay: '1s',
													}}
												/>
											</div>

											{/* AI Powered Stat */}
											<div className='text-center group'>
												<div
													className='font-bold text-lg md:text-xl lg:text-2xl mb-1 transition-all duration-500 group-hover:scale-110'
													style={{
														color: colors.brand.navy,
														textShadow: `0 0 20px ${colors.brand.navy}40`,
													}}>
													AI
												</div>
												<div
													className='text-xs font-medium tracking-wide uppercase'
													style={{ color: colors.neutral.slateGray }}>
													Powered
												</div>
												{/* Animated underline */}
												<div
													className='h-0.5 w-0 group-hover:w-full transition-all duration-500 mx-auto mt-1'
													style={{ background: colors.brand.navy }}
												/>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default HeroBranding;
