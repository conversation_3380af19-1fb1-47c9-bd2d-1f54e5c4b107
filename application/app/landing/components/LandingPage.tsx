/** @format */

import { colors } from '@/app/colors';
import React from 'react';
import { useSectionTransition } from '../utils/scrollAnimations';
import DynamicSectionContainer from './DynamicSectionContainer';
import AIShowcase from './features/AIShowcase';
import CategoryExplorer from './features/CategoryExplorer';
import TechnicalShowcase from './features/TechnicalShowcase';
import LandingFooter from './footer/LandingFooter';
import HeroSection from './hero/HeroSection';
import WaveTransition from './WaveTransition';

interface LandingPageProps {
	onGetStarted: () => void;
}

const LandingPage: React.FC<LandingPageProps> = ({ onGetStarted }) => {
	// Section transition animations
	const heroTransition = useSectionTransition('hero', 'fade-scale');
	const categoryTransition = useSectionTransition('category', 'slide-diagonal');
	const techTransition = useSectionTransition('tech', 'fade-scale');
	const aiTransition = useSectionTransition('ai', 'wave-wash');
	const footerTransition = useSectionTransition('footer', 'fade-scale');

	return (
		<div
			className='min-h-screen relative overflow-hidden'
			style={{
				background: `
          linear-gradient(180deg,
            rgba(14, 165, 233, 0.02) 0%,
            rgba(34, 197, 94, 0.015) 25%,
            rgba(56, 189, 248, 0.01) 50%,
            rgba(74, 222, 128, 0.015) 75%,
            rgba(20, 184, 166, 0.01) 100%
          ),
          ${colors.neutral.cloudWhite}
        `,
			}}>
			{/* Clean Modern Background - No floating elements */}
			{/* Hero Section - No top padding */}
			<section>
				<DynamicSectionContainer sectionType='hero'>
					<div ref={heroTransition.elementRef}>
						<HeroSection onGetStarted={onGetStarted} />
					</div>
				</DynamicSectionContainer>
			</section>

			{/* Wave Transition from Hero to Features - Bottom to Top */}
			<div className='relative'>
				<WaveTransition
					isActive={categoryTransition.isVisible}
					direction='up'
					color={colors.brand.blue}
				/>
			</div>

			{/* Features Section - Category Explorer (Left Aligned) */}
			<section className='py-8 md:py-12 lg:py-16'>
				<DynamicSectionContainer sectionType='section1'>
					<div
						ref={categoryTransition.elementRef}
						className={`transition-all duration-800 ${
							categoryTransition.animationState === 'idle'
								? 'opacity-0 transform translate-x-12 translate-y-8'
								: categoryTransition.animationState === 'entering'
								? 'opacity-70 transform translate-x-6 translate-y-4'
								: 'opacity-100 transform translate-x-0 translate-y-0'
						}`}>
						<CategoryExplorer
							onCategorySelect={(category) =>
								console.log('Selected category:', category)
							}
						/>
					</div>
				</DynamicSectionContainer>
			</section>

			{/* Wave Transition between Features - Bottom to Top */}
			<div className='relative'>
				<WaveTransition
					isActive={aiTransition.isVisible}
					direction='up'
					color={colors.brand.green}
				/>
			</div>

			{/* Features Section - AI Showcase (Right Aligned) */}
			<DynamicSectionContainer sectionType='section2'>
				<div
					ref={aiTransition.elementRef}
					className={`transition-all duration-1000 ${
						aiTransition.animationState === 'idle'
							? 'opacity-0 transform translate-x-8 scale-95'
							: aiTransition.animationState === 'entering'
							? 'opacity-70 transform translate-x-4 scale-98'
							: 'opacity-100 transform translate-x-0 scale-100'
					}`}>
					<AIShowcase onGetStarted={onGetStarted} />
				</div>
			</DynamicSectionContainer>

			{/* Wave Transition to Technical Section */}
			<div className='relative'>
				<WaveTransition
					isActive={techTransition.isVisible}
					direction='up'
					color={colors.brand.navy}
				/>
			</div>

			{/* Technical Showcase Section */}
			<section className='py-8 md:py-12 lg:py-16'>
				<DynamicSectionContainer sectionType='section1'>
					<div
						ref={techTransition.elementRef}
						className={`transition-all duration-800 ${
							techTransition.animationState === 'idle'
								? 'opacity-0 transform translate-y-12 scale-95'
								: techTransition.animationState === 'entering'
								? 'opacity-70 transform translate-y-6 scale-98'
								: 'opacity-100 transform translate-y-0 scale-100'
						}`}>
						<TechnicalShowcase onGetStarted={onGetStarted} />
					</div>
				</DynamicSectionContainer>
			</section>

			{/* Wave Transition to Footer - Bottom to Top */}
			<div className='relative'>
				<WaveTransition
					isActive={footerTransition.isVisible}
					direction='up'
					color={colors.brand.navy}
				/>
			</div>

			{/* Footer Section - Centered */}
			<section className='py-8 md:py-12 lg:py-16'>
				<DynamicSectionContainer sectionType='footer'>
					<div
						ref={footerTransition.elementRef}
						className={`transition-all duration-800 ${
							footerTransition.animationState === 'idle'
								? 'opacity-0 transform scale-90'
								: footerTransition.animationState === 'entering'
								? 'opacity-50 transform scale-95'
								: 'opacity-100 transform scale-100'
						}`}>
						<LandingFooter />
					</div>
				</DynamicSectionContainer>
			</section>
		</div>
	);
};

export default LandingPage;
